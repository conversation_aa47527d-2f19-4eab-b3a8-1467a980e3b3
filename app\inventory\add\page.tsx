"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Loader2, Sparkles, Info } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useFoodItems } from "@/hooks/use-food-items"
import { useCategories } from "@/hooks/use-categories"
import { useUnits } from "@/hooks/use-units"
import { ImageUpload } from "@/components/image-upload"
import { useSettings } from "@/hooks/use-settings"
import { GeminiClient, type AIAnalyzedFoodItem } from "@/lib/gemini-client"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AddItemPage() {
  const router = useRouter()
  const { settings, hasGeminiApiKey, getSelectedGeminiModel } = useSettings()
  const { categories, isLoading: categoriesLoading } = useCategories()
  const { units, isLoading: unitsLoading } = useUnits()
  const [formData, setFormData] = useState({
    name: "",
    quantity: "",
    unit: "",
    expirationDate: "",
    category: "",
    imageUrl: "",
  })

  const [aiAnalysisLoading, setAiAnalysisLoading] = useState(false)
  const [aiAnalysisError, setAiAnalysisError] = useState<string | null>(null)
  const [aiGeneratedInfo, setAiGeneratedInfo] = useState<AIAnalyzedFoodItem | null>(null)

  const { addItem } = useFoodItems()

  const handleImageReadyForAI = async (base64Image: string, mimeType: string) => {
    if (!hasGeminiApiKey || !settings.geminiApiKey) {
      setAiAnalysisError("Gemini API key not configured. Please set it up in Settings to enable AI analysis.")
      return
    }

    setAiAnalysisLoading(true)
    setAiAnalysisError(null)
    setAiGeneratedInfo(null)

    try {
      const selectedModel = getSelectedGeminiModel()
      if (!selectedModel) {
        setAiAnalysisError("No AI model selected. Please go to Settings and select a Gemini model to enable AI analysis.")
        return
      }

      const geminiClient = new GeminiClient(settings.geminiApiKey, selectedModel)
      const result = await geminiClient.analyzeFoodImage(base64Image, mimeType)

      if (result.success && result.data) {
        const data: AIAnalyzedFoodItem = result.data
        setAiGeneratedInfo(data)
        // Pre-fill form fields with AI-generated data
        setFormData((prev) => ({
          ...prev,
          name: data.name || prev.name,
          category:
            data.category && categories.some(cat => cat.name === data.category.toLowerCase())
              ? data.category.toLowerCase()
              : prev.category,
        }))
      } else {
        setAiAnalysisError(result.error || "Failed to analyze image with AI.")
      }
    } catch (error: unknown) {
      setAiAnalysisError(error instanceof Error ? error.message : "An unexpected error occurred during AI analysis.")
    } finally {
      setAiAnalysisLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.quantity || !formData.unit || !formData.expirationDate || !formData.category) {
      return
    }

    try {
      await addItem({
        name: formData.name,
        quantity: Number.parseFloat(formData.quantity),
        unit: formData.unit,
        expiration_date: formData.expirationDate,
        category: formData.category,
        image_url: formData.imageUrl || undefined,
      })

      router.push("/inventory")
    } catch (error) {
      console.error("Failed to add item:", error)
      alert("Failed to add item. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center gap-3">
            <Link href="/inventory">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Add Food Item</h1>
          </div>
        </div>
      </div>

      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Image Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Photo & AI Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ImageUpload
                currentImageUrl={formData.imageUrl}
                onImageUpload={(url) => setFormData({ ...formData, imageUrl: url })}
                onImageRemove={() => setFormData({ ...formData, imageUrl: "" })}
                onImageReadyForAI={handleImageReadyForAI}
                type="food-item"
                disabled={aiAnalysisLoading} // Disable upload while AI is processing
              />

              {aiAnalysisLoading && (
                <div className="flex items-center justify-center py-4 text-gray-600">
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Analyzing image with AI...
                </div>
              )}

              {aiAnalysisError && (
                <Alert variant="destructive" className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertDescription>{aiAnalysisError}</AlertDescription>
                </Alert>
              )}

              {aiGeneratedInfo && (
                <div className="mt-6 space-y-4">
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                    AI-Generated Information
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <Label className="font-medium">Name:</Label>
                      <p>{aiGeneratedInfo.name}</p>
                    </div>
                    <div>
                      <Label className="font-medium">Description:</Label>
                      <p>{aiGeneratedInfo.description}</p>
                    </div>
                    <div>
                      <Label className="font-medium">Category:</Label>
                      <p className="capitalize">{aiGeneratedInfo.category}</p>
                    </div>
                    <div>
                      <Label className="font-medium">Nutritional Info (per 100g):</Label>
                      <ul className="list-disc list-inside ml-4">
                        <li>Calories: {aiGeneratedInfo.caloriesPer100g}</li>
                        <li>Protein: {aiGeneratedInfo.macronutrients.protein}</li>
                        <li>Carbohydrates: {aiGeneratedInfo.macronutrients.carbohydrates}</li>
                        <li>Fat: {aiGeneratedInfo.macronutrients.fat}</li>
                      </ul>
                    </div>
                    {aiGeneratedInfo.keyIngredients.length > 0 && (
                      <div>
                        <Label className="font-medium">Key Ingredients:</Label>
                        <p>{aiGeneratedInfo.keyIngredients.join(", ")}</p>
                      </div>
                    )}
                  </div>
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      The fields below have been pre-filled with AI-generated data. Please review and edit as needed.
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Item Details */}
          <Card>
            <CardHeader>
              <CardTitle>Item Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Item Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., Bananas, Milk, Chicken Breast"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    step="0.1"
                    placeholder="1.5"
                    value={formData.quantity}
                    onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="unit">Unit</Label>
                  <Select
                    value={formData.unit}
                    onValueChange={(value) => setFormData({ ...formData, unit: value })}
                    disabled={unitsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={unitsLoading ? "Loading units..." : "Select unit"} />
                    </SelectTrigger>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.name}>
                          {unit.display_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                  disabled={categoriesLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={categoriesLoading ? "Loading categories..." : "Select category"} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.display_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="expirationDate">Expiration Date</Label>
                <Input
                  id="expirationDate"
                  type="date"
                  value={formData.expirationDate}
                  onChange={(e) => setFormData({ ...formData, expirationDate: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-3">
            <Link href="/inventory" className="flex-1">
              <Button variant="outline" className="w-full bg-transparent">
                Cancel
              </Button>
            </Link>
            <Button type="submit" className="flex-1" disabled={aiAnalysisLoading}>
              Add Item
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
