import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, getLocale } from 'next-intl/server';
import "./globals.css"
import { AuthWrapper } from "@/components/auth-wrapper"
import { QueryProvider } from "@/components/query-provider"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin", "vietnamese"] })

export const metadata: Metadata = {
  title: "Food Management App",
  description: "Mobile food management and meal planning application",
  generator: "v0.dev",
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Get current locale and messages
  const messages = await getMessages();
  const locale = await getLocale();

  return (
    <html lang={locale || 'en'}>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages}>
          <QueryProvider>
            {/* AuthWrapper is a client component but now safely nested
                inside the client-side QueryProvider */}
            <AuthWrapper>{children}</AuthWrapper>
            <Toaster />
          </QueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
