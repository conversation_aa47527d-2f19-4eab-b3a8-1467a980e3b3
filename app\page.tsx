"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Bell, Package, Calendar, ShoppingCart, ChefHat, Plus, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { BottomNav } from "@/components/bottom-nav"
import { useFoodItems } from "@/hooks/use-food-items"
import { useRecipes } from "@/hooks/use-recipes"
import { useMealPlans } from "@/hooks/use-meal-plans"
import { UserMenu } from "@/components/user-menu"
import { RecipeSuggestions } from "@/components/recipe-suggestions"
import { useTranslations } from 'next-intl'
import type { Database } from "@/lib/supabase"

import { formatVietnameseDate } from "@/lib/i18n-utils"

export default function Dashboard() {
  const { items: inventory } = useFoodItems()
  const { recipes } = useR<PERSON>ip<PERSON>()
  const { mealPlans } = useMealPlans()
  const [expiringItems, setExpiringItems] = useState<Database["public"]["Tables"]["food_items"]["Row"][]>([])

  const t = useTranslations('dashboard')
  const tCommon = useTranslations('common')

  useEffect(() => {
    // Check for expiring items (within 3 days)
    const today = new Date()
    const threeDaysFromNow = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000)

    const expiring = inventory.filter((item) => {
      const expirationDate = new Date(item.expiration_date)
      return expirationDate <= threeDaysFromNow && expirationDate >= today
    })

    setExpiringItems(expiring)
  }, [inventory])

  const todaysMeals = mealPlans
    .filter((plan) => {
      const today = new Date().toISOString().split("T")[0]
      return plan.date === today
    })
    .map((plan) => ({
      ...plan,
      recipeName: plan.recipe?.name || "Công thức không xác định",
      meal: plan.meal_type, // Map meal_type to meal for backward compatibility
    }))

  const inventoryByCategory = inventory.reduce(
    (acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1
      return acc
    },
    {} as Record<string, number>,
  )

  // Get available ingredients for recipe suggestions
  const availableIngredients = inventory
    .filter((item) => {
      const expirationDate = new Date(item.expiration_date)
      const today = new Date()
      return expirationDate > today // Only include non-expired items
    })
    .map((item) => item.name)

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
            <div className="flex items-center gap-2">
              {expiringItems.length > 0 && (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <Bell className="w-3 h-3" />
                  {expiringItems.length}
                </Badge>
              )}
              <UserMenu />
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Expiring Items Alert */}
        {expiringItems.length > 0 && (
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader className="pb-3">
              <CardTitle className="text-orange-800 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Thực phẩm sắp hết hạn
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {expiringItems.slice(0, 3).map((item) => (
                  <div key={item.id} className="flex justify-between items-center text-sm">
                    <span className="font-medium">{item.name}</span>
                    <span className="text-orange-600">{formatVietnameseDate(new Date(item.expiration_date))}</span>
                  </div>
                ))}
                {expiringItems.length > 3 && (
                  <p className="text-sm text-orange-600">+{expiringItems.length - 3} mặt hàng khác</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* AI Recipe Suggestions */}
        <RecipeSuggestions ingredients={availableIngredients} />

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{inventory.length}</p>
                  <p className="text-sm text-gray-600">Mặt hàng trong kho</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <ChefHat className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{recipes.length}</p>
                  <p className="text-sm text-gray-600">Công thức đã lưu</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Today's Meals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Bữa ăn hôm nay
            </CardTitle>
          </CardHeader>
          <CardContent>
            {todaysMeals.length > 0 ? (
              <div className="space-y-3">
                {todaysMeals.map((meal) => (
                  <div key={meal.id} className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{meal.recipeName}</p>
                      <p className="text-sm text-gray-600 capitalize">{meal.meal}</p>
                    </div>
                    <Badge variant="outline">{meal.meal}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Chưa có bữa ăn nào được lên kế hoạch cho hôm nay</p>
                <Link href="/meal-planning">
                  <Button variant="outline" size="sm" className="mt-2 bg-transparent">
                    Lên kế hoạch bữa ăn
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Inventory Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Kho theo danh mục</CardTitle>
          </CardHeader>
          <CardContent>
            {Object.keys(inventoryByCategory).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(inventoryByCategory).map(([category, count]) => (
                  <div key={category} className="flex justify-between items-center">
                    <span className="capitalize">{category}</span>
                    <Badge variant="secondary">{count} mặt hàng</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Chưa có mặt hàng nào trong kho</p>
                <Link href="/inventory">
                  <Button variant="outline" size="sm" className="mt-2 bg-transparent">
                    Thêm mặt hàng
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Link href="/inventory/add">
            <Button className="w-full h-12 flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Thêm mặt hàng
            </Button>
          </Link>
          <Link href="/shopping-list">
            <Button variant="outline" className="w-full h-12 flex items-center gap-2 bg-transparent">
              <ShoppingCart className="w-4 h-4" />
              Danh sách mua sắm
            </Button>
          </Link>
        </div>
      </div>

      <BottomNav />
    </div>
  )
}
