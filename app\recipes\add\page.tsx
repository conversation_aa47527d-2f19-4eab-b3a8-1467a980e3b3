"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Plus, X, Sparkles, Loader2 } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useRecipes } from "@/hooks/use-recipes"
import { ImageUpload } from "@/components/image-upload"
import { GeminiClient, type AIGeneratedRecipe } from "@/lib/gemini-client"
import { useSettings } from "@/hooks/use-settings"

export default function AddRecipePage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: "",
    prepTime: "",
    servings: "",
    ingredients: [""],
    instructions: [""],
    imageUrl: "",
  })

  // AI generation state
  const [aiGenerating, setAiGenerating] = useState(false)
  const [aiError, setAiError] = useState<string | null>(null)
  const [aiGeneratedData, setAiGeneratedData] = useState<AIGeneratedRecipe | null>(null)

  const { addRecipe } = useRecipes()
  const { settings, hasGeminiApiKey, getSelectedGeminiModel } = useSettings()

  const generateRecipeWithAI = async () => {
    if (!formData.name.trim()) {
      setAiError("Please enter a recipe name first")
      return
    }

    if (!hasGeminiApiKey || !settings.geminiApiKey) {
      setAiError("Gemini API key not configured. Please set it up in Settings.")
      return
    }

    setAiGenerating(true)
    setAiError(null)
    setAiGeneratedData(null)

    try {
      const selectedModel = getSelectedGeminiModel()
      if (!selectedModel) {
        setAiError("No AI model selected. Please go to Settings and select a Gemini model to enable AI recipe generation.")
        return
      }

      const client = new GeminiClient(settings.geminiApiKey, selectedModel)
      const result = await client.generateCompleteRecipe(formData.name)

      if (result.success && result.data) {
        const generatedRecipe: AIGeneratedRecipe = result.data
        setAiGeneratedData(generatedRecipe)

        // Populate form with AI-generated data
        setFormData(prev => ({
          ...prev,
          prepTime: generatedRecipe.prepTime.toString(),
          servings: generatedRecipe.servings.toString(),
          ingredients: generatedRecipe.ingredients.length > 0 ? generatedRecipe.ingredients : [""],
          instructions: generatedRecipe.instructions.length > 0 ? generatedRecipe.instructions : [""],
        }))
      } else {
        setAiError(result.error || "Failed to generate recipe with AI")
      }
    } catch (error: unknown) {
      setAiError(error instanceof Error ? error.message : "Failed to generate recipe with AI")
    } finally {
      setAiGenerating(false)
    }
  }

  const clearAiData = () => {
    setAiGeneratedData(null)
    setAiError(null)
  }

  const addIngredient = () => {
    setFormData({
      ...formData,
      ingredients: [...formData.ingredients, ""],
    })
  }

  const removeIngredient = (index: number) => {
    setFormData({
      ...formData,
      ingredients: formData.ingredients.filter((_, i) => i !== index),
    })
  }

  const updateIngredient = (index: number, value: string) => {
    const newIngredients = [...formData.ingredients]
    newIngredients[index] = value
    setFormData({
      ...formData,
      ingredients: newIngredients,
    })
  }

  const addInstruction = () => {
    setFormData({
      ...formData,
      instructions: [...formData.instructions, ""],
    })
  }

  const removeInstruction = (index: number) => {
    setFormData({
      ...formData,
      instructions: formData.instructions.filter((_, i) => i !== index),
    })
  }

  const updateInstruction = (index: number, value: string) => {
    const newInstructions = [...formData.instructions]
    newInstructions[index] = value
    setFormData({
      ...formData,
      instructions: newInstructions,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.prepTime || !formData.servings) {
      alert("Please fill in all required fields")
      return
    }

    const filteredIngredients = formData.ingredients.filter((ing) => ing.trim() !== "")
    const filteredInstructions = formData.instructions.filter((inst) => inst.trim() !== "")

    if (filteredIngredients.length === 0 || filteredInstructions.length === 0) {
      alert("Please add at least one ingredient and one instruction")
      return
    }

    try {
      await addRecipe({
        name: formData.name,
        prep_time: Number.parseInt(formData.prepTime),
        servings: Number.parseInt(formData.servings),
        ingredients: filteredIngredients,
        instructions: filteredInstructions,
        image_url: formData.imageUrl || undefined,
      })

      router.push("/recipes")
    } catch (error) {
      console.error("Failed to add recipe:", error)
      alert("Failed to add recipe. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center gap-3">
            <Link href="/recipes">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Add Recipe</h1>
          </div>
        </div>
      </div>

      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Recipe Image */}
          <Card>
            <CardHeader>
              <CardTitle>Recipe Photo</CardTitle>
            </CardHeader>
            <CardContent>
              <ImageUpload
                currentImageUrl={formData.imageUrl}
                onImageUpload={(url) => setFormData({ ...formData, imageUrl: url })}
                onImageRemove={() => setFormData({ ...formData, imageUrl: "" })}
                type="recipe"
              />
            </CardContent>
          </Card>

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Recipe Name</Label>
                <div className="flex gap-2">
                  <Input
                    id="name"
                    placeholder="e.g., Spaghetti Carbonara"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData({ ...formData, name: e.target.value })
                      // Clear AI error when user starts typing
                      if (aiError) setAiError(null)
                    }}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateRecipeWithAI}
                    disabled={aiGenerating || !formData.name.trim() || !hasGeminiApiKey}
                    className="shrink-0 min-w-[44px]"
                    aria-label="Generate recipe with AI"
                  >
                    {aiGenerating ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Sparkles className="w-4 h-4" />
                    )}
                    <span className="hidden sm:inline ml-2">Generate with AI</span>
                  </Button>
                </div>
                {aiError && (
                  <p className="text-sm text-red-600 mt-1" role="alert">
                    {aiError}
                  </p>
                )}
                {!hasGeminiApiKey && (
                  <p className="text-sm text-gray-600 mt-1">
                    Configure Gemini API key in Settings to enable AI generation
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="prepTime">Prep Time (minutes)</Label>
                  <Input
                    id="prepTime"
                    type="number"
                    placeholder="30"
                    value={formData.prepTime}
                    onChange={(e) => setFormData({ ...formData, prepTime: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="servings">Servings</Label>
                  <Input
                    id="servings"
                    type="number"
                    placeholder="4"
                    value={formData.servings}
                    onChange={(e) => setFormData({ ...formData, servings: e.target.value })}
                  />
                </div>
              </div>

              {aiGeneratedData && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg flex items-center gap-2 text-blue-900">
                      <Sparkles className="w-5 h-5 text-blue-600" />
                      AI-Generated Recipe Details
                    </h3>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={clearAiData}
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                      aria-label="Dismiss AI-generated information"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="space-y-2 text-sm text-blue-800">
                    {aiGeneratedData.description && (
                      <div>
                        <span className="font-medium">Description:</span>
                        <p className="mt-1">{aiGeneratedData.description}</p>
                      </div>
                    )}
                    {aiGeneratedData.difficulty && (
                      <div>
                        <span className="font-medium">Difficulty:</span>
                        <span className="ml-2">{aiGeneratedData.difficulty}</span>
                      </div>
                    )}
                    <p className="text-xs text-blue-600 mt-3">
                      The form has been populated with AI-generated content. You can edit any field as needed.
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Ingredients */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Ingredients</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addIngredient}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.ingredients.map((ingredient, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="e.g., 2 cups flour"
                      value={ingredient}
                      onChange={(e) => updateIngredient(index, e.target.value)}
                    />
                    {formData.ingredients.length > 1 && (
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeIngredient(index)}>
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Instructions</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addInstruction}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.instructions.map((instruction, index) => (
                  <div key={index} className="flex gap-2">
                    <div className="flex-1">
                      <Label className="text-sm text-gray-600">Step {index + 1}</Label>
                      <Textarea
                        placeholder="Describe this step..."
                        value={instruction}
                        onChange={(e) => updateInstruction(index, e.target.value)}
                        rows={2}
                      />
                    </div>
                    {formData.instructions.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeInstruction(index)}
                        className="mt-6"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-3">
            <Link href="/recipes" className="flex-1">
              <Button variant="outline" className="w-full bg-transparent">
                Cancel
              </Button>
            </Link>
            <Button type="submit" className="flex-1">
              Save Recipe
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
