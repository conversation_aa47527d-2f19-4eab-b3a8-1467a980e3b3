"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Plus, Search, ChefHat } from "lucide-react"
import Link from "next/link"
import { BottomNav } from "@/components/bottom-nav"
import { useRecipes } from "@/hooks/use-recipes"
import { RecipeCard } from "@/components/recipe-card"

export default function RecipesPage() {
  const { recipes, deleteRecipe, loading } = useRecipes()
  const [searchTerm, setSearchTerm] = useState("")

  const handleDeleteRecipe = async (id: string) => {
    try {
      await deleteRecipe(id)
    } catch (error) {
      console.error("Failed to delete recipe:", error)
    }
  }

  const filteredRecipes = recipes.filter(
    (recipe) =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.ingredients.some((ingredient) => ingredient.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading recipes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">Recipes</h1>
            <Link href="/recipes/add">
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Recipe
              </Button>
            </Link>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search recipes or ingredients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <div className="p-4">
        {filteredRecipes.length > 0 ? (
          <div className="space-y-4">
            {filteredRecipes.map((recipe) => (
              <RecipeCard key={recipe.id} recipe={recipe} onDelete={handleDeleteRecipe} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ChefHat className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? "No recipes found" : "No recipes yet"}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? "Try adjusting your search terms" : "Start building your recipe collection"}
            </p>
            <Link href="/recipes/add">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Recipe
              </Button>
            </Link>
          </div>
        )}
      </div>

      <BottomNav />
    </div>
  )
}
