"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ArrowLeft, <PERSON>tings, Bell, Shield, Palette, Loader2, Globe } from "lucide-react"
import Link from "next/link"
import { BottomNav } from "@/components/bottom-nav"
import { useSettings } from "@/hooks/use-settings"
import { GeminiApiKeySetup } from "@/components/gemini-api-key-setup"
import { GeminiModelSelector } from "@/components/gemini-model-selector"
import { CategoriesUnitsManager } from "@/components/categories-units-manager"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LanguageSelector } from "@/components/language-selector"
import { useTranslations } from 'next-intl'

export default function SettingsPage() {
  const {
    settings,
    isLoading,
    isError,
    error,
    updateGemini<PERSON><PERSON><PERSON><PERSON>,
    removeG<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    update<PERSON><PERSON><PERSON>s,
    hasGeminiApiKey,
    hasApiKeyInDb,
    getSelectedGeminiModel,
  } = useSettings()
  const t = useTranslations('settings')

  const handlePreferenceChange = async (key: string, value: string | boolean | number) => {
    await updatePreferences({ [key]: value })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 flex items-center justify-center">
        <Alert variant="destructive">
          <AlertDescription>Error loading settings: {error?.message || "An unknown error occurred."}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center gap-3">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">{t('title')}</h1>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Gemini API Configuration */}
        <GeminiApiKeySetup
          currentApiKey={settings.geminiApiKey}
          onSave={updateGeminiApiKey}
          onRemove={removeGeminiApiKey}
          loading={isLoading}
          hasApiKeyInDb={hasApiKeyInDb}
        />

        {/* Gemini Model Selection */}
        <GeminiModelSelector
          currentModel={getSelectedGeminiModel() ?? undefined}
          apiKey={settings.geminiApiKey}
          onModelChange={async (modelId: string) => {
            try {
              await updatePreferences({ selectedGeminiModel: modelId })
              return { success: true }
            } catch (error: unknown) {
              return { success: false, error: error instanceof Error ? error.message : "Failed to save model selection" }
            }
          }}
          loading={isLoading}
          disabled={!hasGeminiApiKey}
        />

        {/* Categories & Units Management */}
        <CategoriesUnitsManager />

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              {t('notifications')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="expiration-alerts">{t('expirationReminders')}</Label>
                <p className="text-sm text-gray-600">Get notified when food items are about to expire</p>
              </div>
              <Switch
                id="expiration-alerts"
                checked={settings.preferences.expirationAlerts !== false}
                onCheckedChange={(checked) => handlePreferenceChange("expirationAlerts", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="recipe-suggestions">Recipe Suggestions</Label>
                <p className="text-sm text-gray-600">Get AI-powered recipe suggestions based on your inventory</p>
              </div>
              <Switch
                id="recipe-suggestions"
                checked={settings.preferences.recipeSuggestions !== false}
                onCheckedChange={(checked) => handlePreferenceChange("recipeSuggestions", checked)}
                disabled={!hasGeminiApiKey}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="shopping-reminders">Shopping Reminders</Label>
                <p className="text-sm text-gray-600">Get reminded to go shopping when inventory is low</p>
              </div>
              <Switch
                id="shopping-reminders"
                checked={settings.preferences.shoppingReminders !== false}
                onCheckedChange={(checked) => handlePreferenceChange("shoppingReminders", checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Privacy & Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Privacy & Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="data-sync">Data Synchronization</Label>
                <p className="text-sm text-gray-600">Sync your data across devices</p>
              </div>
              <Switch
                id="data-sync"
                checked={settings.preferences.dataSync !== false}
                onCheckedChange={(checked) => handlePreferenceChange("dataSync", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="offline-mode">Offline Mode</Label>
                <p className="text-sm text-gray-600">Allow app to work offline with cached data</p>
              </div>
              <Switch
                id="offline-mode"
                checked={settings.preferences.offlineMode !== false}
                onCheckedChange={(checked) => handlePreferenceChange("offlineMode", checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              Appearance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="compact-view">Compact View</Label>
                <p className="text-sm text-gray-600">Show more items in lists with smaller cards</p>
              </div>
              <Switch
                id="compact-view"
                checked={settings.preferences.compactView === true}
                onCheckedChange={(checked) => handlePreferenceChange("compactView", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="show-images">Show Images</Label>
                <p className="text-sm text-gray-600">Display food and recipe images</p>
              </div>
              <Switch
                id="show-images"
                checked={settings.preferences.showImages !== false}
                onCheckedChange={(checked) => handlePreferenceChange("showImages", checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Language & Localization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              {t('language')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="language-selector">{t('selectLanguage')}</Label>
                <p className="text-sm text-gray-600">
                  Choose your preferred language for the interface
                </p>
              </div>
              <LanguageSelector variant="compact" />
            </div>
          </CardContent>
        </Card>

        {/* App Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              App Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Version</span>
              <span>1.0.0</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Last Updated</span>
              <span>{new Date().toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Gemini API Status</span>
              <span className={hasGeminiApiKey ? "text-green-600" : "text-gray-500"}>
                {hasGeminiApiKey ? "Configured" : "Not Configured"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <BottomNav />
    </div>
  )
}
