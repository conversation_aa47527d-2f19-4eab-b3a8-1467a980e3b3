"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Trash2, Refresh<PERSON>w } from "lucide-react"
import { BottomNav } from "@/components/bottom-nav"
import { AddShoppingItemDialog } from "@/components/add-shopping-item-dialog"
import { useShoppingList } from "@/hooks/use-shopping-list"
import { useFoodItems } from "@/hooks/use-food-items"
import { useCategories } from "@/hooks/use-categories"
import { useMealPlans } from "@/hooks/use-meal-plans"
import { useRecipes } from "@/hooks/use-recipes"
import { toast } from "sonner"

export default function ShoppingListPage() {
  const { items: shoppingList, addItem, updateItem, deleteItem, clearCompleted } = useShoppingList()
  const { items: inventory } = useFoodItems()
  const { categories } = useCategories()
  const { mealPlans } = useMealPlans()
  const { recipes } = useRecipes()

  const toggleItem = async (id: string) => {
    const item = shoppingList.find((item) => item.id === id)
    if (item) {
      try {
        await updateItem(id, { completed: !item.completed })
        // Don't show toast for toggle as it's a frequent action
      } catch (error) {
        console.error("Failed to update item:", error)
        toast.error("Failed to update item", {
          description: "There was an error updating the item. Please try again.",
        })
      }
    }
  }

  const removeItem = async (id: string) => {
    try {
      await deleteItem(id)
      toast.success("Item removed", {
        description: "The item has been removed from your shopping list.",
      })
    } catch (error) {
      console.error("Failed to remove item:", error)
      toast.error("Failed to remove item", {
        description: "There was an error removing the item. Please try again.",
      })
    }
  }

  const generateFromMealPlans = async () => {
    try {
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)

      const upcomingMeals = mealPlans.filter((plan) => {
        const planDate = new Date(plan.date)
        return planDate >= new Date() && planDate <= nextWeek
      })

      if (upcomingMeals.length === 0) {
        toast.info("No upcoming meals found", {
          description: "Add some meal plans for the next week to generate a shopping list.",
        })
        return
      }

      const neededIngredients: { [key: string]: { quantity: number; unit: string; category: string } } = {}

      upcomingMeals.forEach((meal) => {
        const recipe = recipes.find((r) => r.id === meal.recipe_id)
        if (recipe) {
          recipe.ingredients.forEach((ingredient) => {
            const parts = ingredient.split(" ")
            const quantity = Number.parseFloat(parts[0]) || 1
            const unit = parts[1] || "pieces"
            const name = parts.slice(2).join(" ") || ingredient

            if (neededIngredients[name]) {
              neededIngredients[name].quantity += quantity
            } else {
              // Find the default category or use 'pantry' as fallback
              const defaultCategory = categories.find(cat => cat.name === "pantry")?.name ||
                                     categories.find(cat => cat.name === "other")?.name ||
                                     "other"
              neededIngredients[name] = {
                quantity,
                unit,
                category: defaultCategory,
              }
            }
          })
        }
      })

      // Remove existing meal-plan items first
      const mealPlanItems = shoppingList.filter((item) => item.source === "meal-plan")
      for (const item of mealPlanItems) {
        await deleteItem(item.id)
      }

      let addedCount = 0
      // Add new items
      for (const [name, details] of Object.entries(neededIngredients)) {
        const inventoryItem = inventory.find(
          (item) =>
            item.name.toLowerCase().includes(name.toLowerCase()) || name.toLowerCase().includes(item.name.toLowerCase()),
        )

        const neededQuantity = inventoryItem ? Math.max(0, details.quantity - inventoryItem.quantity) : details.quantity

        if (neededQuantity > 0) {
          try {
            await addItem({
              name,
              quantity: neededQuantity,
              unit: details.unit,
              category: details.category,
              completed: false,
              source: "meal-plan",
            })
            addedCount++
          } catch (error) {
            console.error("Failed to add shopping item:", error)
          }
        }
      }

      toast.success("Shopping list generated", {
        description: `${addedCount} item${addedCount !== 1 ? 's' : ''} added from your meal plans.`,
      })
    } catch (error) {
      console.error("Failed to generate shopping list:", error)
      toast.error("Failed to generate shopping list", {
        description: "There was an error generating your shopping list. Please try again.",
      })
    }
  }

  const handleAddItem = async (item: {
    name: string
    quantity: number
    unit: string
    category: string
    completed: boolean
    source: "manual"
  }) => {
    try {
      await addItem(item)
      toast.success("Item added to shopping list", {
        description: `${item.name} (${item.quantity} ${item.unit}) added successfully`,
      })
    } catch (error) {
      console.error("Failed to add item:", error)
      toast.error("Failed to add item", {
        description: "There was an error adding the item to your shopping list. Please try again.",
      })
      throw error // Re-throw to let the dialog handle loading state
    }
  }

  const handleClearCompleted = async () => {
    try {
      const completedCount = shoppingList.filter((item) => item.completed).length
      await clearCompleted()
      toast.success("Completed items cleared", {
        description: `${completedCount} completed item${completedCount !== 1 ? 's' : ''} removed from your list.`,
      })
    } catch (error) {
      console.error("Failed to clear completed items:", error)
      toast.error("Failed to clear completed items", {
        description: "There was an error clearing completed items. Please try again.",
      })
    }
  }

  const completedCount = shoppingList.filter((item) => item.completed).length
  const totalCount = shoppingList.length

  const groupedItems = shoppingList.reduce(
    (acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = []
      }
      acc[item.category].push(item)
      return acc
    },
    {} as Record<string, typeof shoppingList>,
  )

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">Shopping List</h1>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={generateFromMealPlans}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Generate
              </Button>
              <AddShoppingItemDialog onAddItem={handleAddItem} />
            </div>
          </div>

          {totalCount > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {completedCount} of {totalCount} completed
              </span>
              {completedCount > 0 && (
                <Button variant="ghost" size="sm" onClick={handleClearCompleted}>
                  Clear Completed
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="p-4">
        {totalCount > 0 ? (
          <div className="space-y-4">
            {Object.entries(groupedItems).map(([category, items]) => {
              const categoryObj = categories.find(cat => cat.name === category)
              const displayName = categoryObj?.display_name || category.charAt(0).toUpperCase() + category.slice(1)

              return (
                <Card key={category}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {displayName}
                      <Badge variant="secondary">{items.length}</Badge>
                    </CardTitle>
                  </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50">
                        <Checkbox checked={item.completed} onCheckedChange={() => toggleItem(item.id)} />
                        <div className={`flex-1 ${item.completed ? "line-through text-gray-500" : ""}`}>
                          <span className="font-medium">{item.name}</span>
                          <span className="text-sm text-gray-600 ml-2">
                            {item.quantity} {item.unit}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {item.source}
                          </Badge>
                          <Button variant="ghost" size="sm" onClick={() => removeItem(item.id)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Shopping list is empty</h3>
            <p className="text-gray-500 mb-4">Add items manually or generate from your meal plans</p>
            <div className="flex gap-3 justify-center">
              <AddShoppingItemDialog onAddItem={handleAddItem} />
              <Button variant="outline" onClick={generateFromMealPlans}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Generate from Meals
              </Button>
            </div>
          </div>
        )}
      </div>

      <BottomNav />
    </div>
  )
}
