"use client"

import { useState } from 'react';
import { Check, ChevronDown, Globe, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage, type SupportedLanguage } from '@/hooks/use-language';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

interface LanguageSelectorProps {
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  showLabel?: boolean;
}

export function LanguageSelector({ 
  variant = 'default', 
  className,
  showLabel = true 
}: LanguageSelectorProps) {
  const { 
    currentLanguage, 
    supportedLanguages, 
    changeLanguage, 
    isChanging 
  } = useLanguage();
  const t = useTranslations('settings');
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = async (languageCode: SupportedLanguage) => {
    setIsOpen(false);
    await changeLanguage(languageCode);
  };

  const renderTrigger = () => {
    const baseClasses = cn(
      "min-h-[44px] min-w-[44px] transition-all duration-200",
      "hover:bg-gray-50 hover:scale-105 active:scale-95",
      "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
      "disabled:opacity-50 disabled:cursor-not-allowed",
      className
    );

    if (variant === 'icon-only') {
      return (
        <Button
          variant="ghost"
          size="sm"
          className={cn(baseClasses, "w-11 h-11 p-0")}
          disabled={isChanging}
          aria-label={`${t('language')}: ${currentLanguage.nativeName}`}
        >
          {isChanging ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Globe className="w-5 h-5" />
          )}
        </Button>
      );
    }

    if (variant === 'compact') {
      return (
        <Button
          variant="ghost"
          size="sm"
          className={cn(baseClasses, "h-11 px-3 gap-2")}
          disabled={isChanging}
        >
          {isChanging ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Globe className="w-4 h-4" />
          )}
          <span className="text-sm font-medium">
            {currentLanguage.code.toUpperCase()}
          </span>
          <ChevronDown className="w-3 h-3 opacity-50" />
        </Button>
      );
    }

    return (
      <Button
        variant="ghost"
        className={cn(baseClasses, "h-11 px-4 gap-3 justify-start")}
        disabled={isChanging}
      >
        {isChanging ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <Globe className="w-5 h-5" />
        )}
        <div className="flex flex-col items-start">
          {showLabel && (
            <span className="text-xs text-gray-500 leading-none">
              {t('language')}
            </span>
          )}
          <span className="text-sm font-medium leading-none">
            {currentLanguage.nativeName}
          </span>
        </div>
        <ChevronDown className="w-4 h-4 opacity-50 ml-auto" />
      </Button>
    );
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        {renderTrigger()}
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-56"
        sideOffset={8}
      >
        {supportedLanguages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={cn(
              "min-h-[44px] px-4 py-3 cursor-pointer",
              "hover:bg-gray-50 focus:bg-gray-50",
              "transition-colors duration-150",
              currentLanguage.code === language.code && "bg-blue-50"
            )}
            disabled={isChanging}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex flex-col">
                <span className="font-medium text-sm">
                  {language.nativeName}
                </span>
                <span className="text-xs text-gray-500">
                  {language.name}
                </span>
              </div>
              {currentLanguage.code === language.code && (
                <Check className="w-4 h-4 text-blue-600" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Preset variants for common use cases
export function LanguageSelectorCompact(props: Omit<LanguageSelectorProps, 'variant'>) {
  return <LanguageSelector {...props} variant="compact" />;
}

export function LanguageSelectorIcon(props: Omit<LanguageSelectorProps, 'variant'>) {
  return <LanguageSelector {...props} variant="icon-only" />;
}

// Mobile-optimized version with larger touch targets
export function LanguageSelectorMobile(props: LanguageSelectorProps) {
  return (
    <LanguageSelector 
      {...props} 
      className={cn("min-h-[48px]", props.className)}
    />
  );
}
