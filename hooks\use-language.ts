"use client"

import { useState, useEffect, useCallback } from 'react';
import { useLocale } from 'next-intl';

export type SupportedLanguage = 'en' | 'vi';

export interface LanguageOption {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
}

export const SUPPORTED_LANGUAGES: LanguageOption[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English'
  },
  {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt'
  }
];

const STORAGE_KEY = 'preferred-language';

export function useLanguage() {
  const currentLocale = useLocale() as SupportedLanguage;
  const [isChanging, setIsChanging] = useState(false);

  // Get current language info
  const currentLanguage = SUPPORTED_LANGUAGES.find(lang => lang.code === currentLocale) || SUPPORTED_LANGUAGES[0];

  // Initialize language from localStorage on client side only once
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(STORAGE_KEY) as SupportedLanguage;
      if (stored && stored !== currentLocale && SUPPORTED_LANGUAGES.some(lang => lang.code === stored)) {
        // If stored language is different from current, trigger a change
        changeLanguage(stored);
      }
    }
    // Only run this effect once on mount, not when currentLocale changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const changeLanguage = useCallback(async (newLanguage: SupportedLanguage) => {
    if (newLanguage === currentLocale) {
      return; // No change needed
    }

    setIsChanging(true);

    try {
      // Store preference in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEY, newLanguage);
      }

      // Set a cookie for the server to pick up the locale preference
      document.cookie = `NEXT_LOCALE=${newLanguage}; path=/; max-age=${60 * 60 * 24 * 365}`;

      // Refresh the page to apply the new locale
      // This is necessary because next-intl needs to re-render with new locale
      window.location.reload();
    } catch (error) {
      console.error('Failed to change language:', error);
      setIsChanging(false);
    }
  }, [currentLocale]);

  const getStoredLanguage = useCallback((): SupportedLanguage | null => {
    if (typeof window === 'undefined') return null;
    
    const stored = localStorage.getItem(STORAGE_KEY) as SupportedLanguage;
    return stored && SUPPORTED_LANGUAGES.some(lang => lang.code === stored) ? stored : null;
  }, []);

  const clearStoredLanguage = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  return {
    currentLanguage,
    currentLocale,
    supportedLanguages: SUPPORTED_LANGUAGES,
    changeLanguage,
    isChanging,
    getStoredLanguage,
    clearStoredLanguage
  };
}

// Utility function to get language name by code
export function getLanguageName(code: SupportedLanguage, useNative = false): string {
  const language = SUPPORTED_LANGUAGES.find(lang => lang.code === code);
  if (!language) return code;
  return useNative ? language.nativeName : language.name;
}

// Utility function to detect browser language
export function detectBrowserLanguage(): SupportedLanguage {
  if (typeof window === 'undefined') return 'en';
  
  const browserLang = navigator.language.split('-')[0] as SupportedLanguage;
  return SUPPORTED_LANGUAGES.some(lang => lang.code === browserLang) ? browserLang : 'en';
}
