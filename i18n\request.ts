import { getRequestConfig } from 'next-intl/server';

// Supported locales
const locales = ['en', 'vi'];
const defaultLocale = 'en';

export default getRequestConfig(async ({ requestLocale }) => {
  // This can be any value or logic based on the request
  let locale = await requestLocale;

  // Fallback to default if not supported
  if (!locale || !locales.includes(locale)) {
    locale = defaultLocale;
  }

  // Get timezone and currency based on locale
  const getLocaleConfig = (locale: string) => {
    switch (locale) {
      case 'vi':
        return {
          timeZone: 'Asia/Ho_Chi_Minh',
          currency: 'VND'
        };
      case 'en':
      default:
        return {
          timeZone: 'UTC',
          currency: 'USD'
        };
    }
  };

  const config = getLocaleConfig(locale);

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
    timeZone: config.timeZone,
    now: new Date(),
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        medium: {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        precise: {
          maximumFractionDigits: 5
        },
        currency: {
          style: 'currency',
          currency: config.currency
        }
      },
      list: {
        enumeration: {
          style: 'long',
          type: 'conjunction'
        }
      }
    }
  };
});

export { locales, defaultLocale };
