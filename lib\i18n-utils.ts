import { useTranslations } from 'next-intl';

// Utility functions for common translation patterns
export function useCommonTranslations() {
  const t = useTranslations('common');
  
  return {
    loading: t('loading'),
    save: t('save'),
    cancel: t('cancel'),
    delete: t('delete'),
    edit: t('edit'),
    add: t('add'),
    remove: t('remove'),
    confirm: t('confirm'),
    close: t('close'),
    back: t('back'),
    next: t('next'),
    previous: t('previous'),
    search: t('search'),
    filter: t('filter'),
    sort: t('sort'),
    clear: t('clear'),
    refresh: t('refresh'),
    submit: t('submit'),
    update: t('update'),
    create: t('create'),
    view: t('view'),
    settings: t('settings'),
    home: t('home'),
    yes: t('yes'),
    no: t('no'),
    ok: t('ok'),
    error: t('error'),
    success: t('success'),
    warning: t('warning'),
    info: t('info')
  };
}

export function useNavigationTranslations() {
  const t = useTranslations('navigation');
  
  return {
    home: t('home'),
    inventory: t('inventory'),
    meals: t('meals'),
    recipes: t('recipes'),
    shopping: t('shopping'),
    settings: t('settings')
  };
}

export function useAuthTranslations() {
  const t = useTranslations('auth');
  
  return {
    signIn: t('signIn'),
    signUp: t('signUp'),
    signOut: t('signOut'),
    email: t('email'),
    password: t('password'),
    confirmPassword: t('confirmPassword'),
    forgotPassword: t('forgotPassword'),
    rememberMe: t('rememberMe'),
    createAccount: t('createAccount'),
    alreadyHaveAccount: t('alreadyHaveAccount'),
    dontHaveAccount: t('dontHaveAccount'),
    signInWithGoogle: t('signInWithGoogle'),
    signInWithFacebook: t('signInWithFacebook'),
    resetPassword: t('resetPassword'),
    checkEmail: t('checkEmail'),
    invalidCredentials: t('invalidCredentials'),
    accountCreated: t('accountCreated'),
    passwordReset: t('passwordReset'),
    signedOut: t('signedOut')
  };
}

export function useErrorTranslations() {
  const t = useTranslations('errors');
  
  return {
    general: t('general'),
    networkError: t('networkError'),
    serverError: t('serverError'),
    notFound: t('notFound'),
    unauthorized: t('unauthorized'),
    forbidden: t('forbidden'),
    validationError: t('validationError'),
    requiredField: t('requiredField'),
    invalidEmail: t('invalidEmail'),
    invalidPassword: t('invalidPassword'),
    passwordTooShort: t('passwordTooShort'),
    passwordMismatch: t('passwordMismatch'),
    invalidDate: t('invalidDate'),
    invalidNumber: t('invalidNumber'),
    invalidUrl: t('invalidUrl'),
    fileTooLarge: t('fileTooLarge'),
    fileTypeNotSupported: t('fileTypeNotSupported'),
    uploadFailed: t('uploadFailed'),
    downloadFailed: t('downloadFailed'),
    saveFailed: t('saveFailed'),
    loadFailed: t('loadFailed'),
    deleteFailed: t('deleteFailed'),
    updateFailed: t('updateFailed'),
    createFailed: t('createFailed'),
    connectionTimeout: t('connectionTimeout'),
    requestTimeout: t('requestTimeout'),
    tooManyRequests: t('tooManyRequests'),
    serviceUnavailable: t('serviceUnavailable'),
    maintenanceMode: t('maintenanceMode'),
    tryAgainLater: t('tryAgainLater'),
    contactSupport: t('contactSupport')
  };
}

// Re-export Vietnamese formatting utilities for convenience
export {
  formatVietnameseDate,
  formatVietnameseTime,
  formatVietnameseDateTime,
  formatVietnameseNumber,
  formatVietnameseCurrency,
  formatVietnamesePercentage,
  formatVietnameseRelativeTime,
  formatVietnameseFileSize,
  formatVietnameseDuration,
  formatVietnameseQuantity,
  getVietnameseDayName,
  getVietnameseMonthName,
  isToday,
  isTomorrow,
  getCurrentVietnameseTime,
  VIETNAMESE_LOCALE,
  VIETNAMESE_TIMEZONE,
  VIETNAMESE_CURRENCY
} from './vietnamese-formatting';

// Vietnamese-specific text utilities
export function capitalizeVietnamese(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

export function pluralizeVietnamese(count: number, singular: string, plural?: string): string {
  // Vietnamese doesn't have plural forms like English, but we can add quantity indicators
  if (count === 1) {
    return `1 ${singular}`;
  }
  return `${count} ${plural || singular}`;
}
