{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "confirm": "Confirm", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "refresh": "Refresh", "submit": "Submit", "update": "Update", "create": "Create", "view": "View", "settings": "Settings", "home": "Home", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"home": "Home", "inventory": "Inventory", "meals": "Meals", "recipes": "Recipes", "shopping": "Shopping", "settings": "Settings"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInWithGoogle": "Sign in with Google", "signInWithFacebook": "Sign in with Facebook", "resetPassword": "Reset Password", "checkEmail": "Check your email", "invalidCredentials": "Invalid credentials", "accountCreated": "Account created successfully", "passwordReset": "Password reset link sent", "signedOut": "Signed out successfully"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "overview": "Overview", "quickStats": "Quick Stats", "recentActivity": "Recent Activity", "upcomingMeals": "Upcoming Meals", "expiringItems": "Expiring Items", "lowStock": "Low Stock", "recipeSuggestions": "Recipe Suggestions", "todaysMeals": "Today's Meals", "inventoryOverview": "Inventory Overview", "noMealsPlanned": "No meals planned for today", "noExpiringItems": "No items expiring soon", "addMealPlan": "Add Meal Plan", "viewInventory": "View Inventory", "planMeals": "Plan Meals"}, "inventory": {"title": "Food Inventory", "addItem": "Add Item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "itemName": "Item Name", "quantity": "Quantity", "unit": "Unit", "category": "Category", "expirationDate": "Expiration Date", "addedDate": "Added Date", "status": "Status", "fresh": "Fresh", "expiringSoon": "Expiring Soon", "expired": "Expired", "noItems": "No items in inventory", "searchPlaceholder": "Search items...", "filterByCategory": "Filter by category", "sortBy": "Sort by", "sortByName": "Name", "sortByDate": "Date", "sortByExpiration": "Expiration Date", "itemAdded": "Item added successfully", "itemUpdated": "Item updated successfully", "itemDeleted": "Item deleted successfully", "confirmDelete": "Are you sure you want to delete this item?", "uploadImage": "Upload Image", "takePhoto": "Take Photo", "removeImage": "Remove Image"}, "recipes": {"title": "Recipes", "addRecipe": "Add Recipe", "editRecipe": "Edit Recipe", "deleteRecipe": "Delete Recipe", "recipeName": "Recipe Name", "description": "Description", "ingredients": "Ingredients", "instructions": "Instructions", "prepTime": "Prep Time", "cookTime": "<PERSON> Time", "servings": "Servings", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "easy": "Easy", "medium": "Medium", "hard": "Hard", "cuisine": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Tags", "noRecipes": "No recipes found", "searchRecipes": "Search recipes...", "filterByCuisine": "Filter by cuisine", "filterByDifficulty": "Filter by difficulty", "recipeAdded": "Recipe added successfully", "recipeUpdated": "Recipe updated successfully", "recipeDeleted": "Recipe deleted successfully", "confirmDeleteRecipe": "Are you sure you want to delete this recipe?", "addIngredient": "Add Ingredient", "removeIngredient": "Remove Ingredient", "addStep": "Add Step", "removeStep": "Remove Step", "generateWithAI": "Generate with AI", "aiSuggestions": "AI Suggestions", "nutritionInfo": "Nutrition Information", "calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fat": "Fat", "fiber": "Fiber"}, "mealPlanning": {"title": "Meal Planning", "addMealPlan": "Add Meal Plan", "editMealPlan": "Edit Meal Plan", "deleteMealPlan": "Delete Meal Plan", "selectDate": "Select Date", "selectMeal": "Select Meal", "selectRecipe": "Select Recipe", "breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack", "noMealPlans": "No meal plans found", "mealPlanAdded": "Meal plan added successfully", "mealPlanUpdated": "Meal plan updated successfully", "mealPlanDeleted": "Meal plan deleted successfully", "confirmDeleteMealPlan": "Are you sure you want to delete this meal plan?", "weekView": "Week View", "monthView": "Month View", "dayView": "Day View", "today": "Today", "tomorrow": "Tomorrow", "thisWeek": "This Week", "nextWeek": "Next Week", "generateShoppingList": "Generate Shopping List", "nutritionSummary": "Nutrition Summary"}, "shoppingList": {"title": "Shopping List", "addItem": "Add Item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "itemName": "Item Name", "quantity": "Quantity", "unit": "Unit", "category": "Category", "notes": "Notes", "completed": "Completed", "pending": "Pending", "noItems": "Shopping list is empty", "clearCompleted": "Clear Completed", "markAllCompleted": "<PERSON> Completed", "markAllPending": "<PERSON> Pending", "generateFromMealPlans": "Generate from Meal Plans", "generateFromInventory": "Generate from Inventory", "itemAdded": "Item added successfully", "itemUpdated": "Item updated successfully", "itemDeleted": "Item deleted successfully", "itemCompleted": "Item marked as completed", "itemUncompleted": "Item marked as pending", "confirmDelete": "Are you sure you want to delete this item?", "confirmClearCompleted": "Are you sure you want to clear all completed items?", "sortByCategory": "Sort by Category", "sortByName": "Sort by Name", "sortByStatus": "Sort by Status", "filterByCategory": "Filter by Category", "filterByStatus": "Filter by Status", "searchPlaceholder": "Search items...", "totalItems": "Total Items", "completedItems": "Completed", "pendingItems": "Pending", "estimatedCost": "Estimated Cost", "addToInventory": "Add to Inventory"}, "settings": {"title": "Settings", "profile": "Profile", "preferences": "Preferences", "notifications": "Notifications", "privacy": "Privacy", "about": "About", "language": "Language", "theme": "Theme", "units": "Units", "categories": "Categories", "backup": "Backup", "restore": "Rest<PERSON>", "export": "Export Data", "import": "Import Data", "deleteAccount": "Delete Account", "signOut": "Sign Out", "version": "Version", "buildNumber": "Build Number", "lastUpdated": "Last Updated", "contactSupport": "Contact Support", "reportBug": "Report Bug", "rateApp": "Rate App", "shareApp": "Share App", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "systemTheme": "System Theme", "metric": "Metric", "imperial": "Imperial", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "expirationReminders": "Expiration Reminders", "mealReminders": "<PERSON><PERSON> Reminders", "shoppingReminders": "Shopping Reminders", "recipeSuggestions": "Recipe Suggestions", "nutritionTracking": "Nutrition Tracking", "autoBackup": "Auto Backup", "dataSync": "Data Sync", "offlineMode": "Offline Mode", "cacheSize": "<PERSON><PERSON>", "clearCache": "<PERSON>ache", "resetSettings": "Reset Settings", "confirmResetSettings": "Are you sure you want to reset all settings?", "confirmDeleteAccount": "Are you sure you want to delete your account? This action cannot be undone.", "settingsUpdated": "Settings updated successfully", "settingsReset": "Settings reset successfully", "cacheCleared": "<PERSON><PERSON> cleared successfully", "accountDeleted": "Account deleted", "backupCreated": "Backup created successfully", "dataRestored": "Data restored successfully", "dataExported": "Data exported successfully", "dataImported": "Data imported successfully", "selectLanguage": "Select Language", "languageChanged": "Language changed successfully", "english": "English", "vietnamese": "Tiếng <PERSON>"}, "ai": {"title": "AI Assistant", "geminiApiKey": "Gemini API Key", "enterApiKey": "Enter Gemini API Key", "saveApiKey": "Save API Key", "removeApiKey": "Remove API Key", "testConnection": "Test Connection", "connectionSuccessful": "Connection successful", "connectionFailed": "Connection failed", "invalidApiKey": "Invalid API key", "apiKeyRequired": "API key required to use AI features", "selectModel": "Select Model", "modelNotFound": "Model not found", "modelNotSupported": "Model not supported", "generateRecipe": "Generate Recipe", "generateShoppingList": "Generate Shopping List", "analyzeFoodItem": "Analyze Food Item", "nutritionAnalysis": "Nutrition Analysis", "recipeSuggestions": "Recipe Suggestions", "mealPlanSuggestions": "Meal Plan Suggestions", "generating": "Generating...", "analyzing": "Analyzing...", "processing": "Processing...", "generationComplete": "Generation complete", "generationFailed": "Generation failed", "analysisComplete": "Analysis complete", "analysisFailed": "Analysis failed", "noSuggestions": "No suggestions available", "tryAgain": "Try Again", "useThisRecipe": "Use This Recipe", "saveRecipe": "Save Recipe", "addToMealPlan": "Add to Meal Plan", "basedOnIngredients": "Based on available ingredients", "healthyOptions": "Healthy Options", "quickMeals": "Quick Meals", "vegetarianOptions": "Vegetarian Options", "lowCalorie": "Low Calorie", "highProtein": "High Protein", "glutenFree": "Gluten Free", "dairyFree": "Dairy Free", "aiPowered": "AI Powered", "smartSuggestions": "Smart Suggestions", "personalizedRecommendations": "Personalized Recommendations"}, "errors": {"general": "An error occurred", "networkError": "Network error", "serverError": "Server error", "notFound": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "validationError": "Validation error", "requiredField": "This field is required", "invalidEmail": "Invalid email", "invalidPassword": "Invalid password", "passwordTooShort": "Password too short", "passwordMismatch": "Passwords don't match", "invalidDate": "Invalid date", "invalidNumber": "Invalid number", "invalidUrl": "Invalid URL", "fileTooLarge": "File too large", "fileTypeNotSupported": "File type not supported", "uploadFailed": "Upload failed", "downloadFailed": "Download failed", "saveFailed": "Save failed", "loadFailed": "Load failed", "deleteFailed": "Delete failed", "updateFailed": "Update failed", "createFailed": "Create failed", "connectionTimeout": "Connection timeout", "requestTimeout": "Request timeout", "tooManyRequests": "Too many requests", "serviceUnavailable": "Service unavailable", "maintenanceMode": "Maintenance mode", "tryAgainLater": "Please try again later", "contactSupport": "Contact support if the problem persists"}, "units": {"weight": {"kg": "kg", "g": "g", "lb": "lb", "oz": "oz"}, "volume": {"l": "liter", "ml": "ml", "cup": "cup", "tbsp": "tbsp", "tsp": "tsp", "fl_oz": "fl oz", "pint": "pint", "quart": "quart", "gallon": "gallon"}, "count": {"piece": "piece", "item": "item", "pack": "pack", "box": "box", "bottle": "bottle", "can": "can", "bag": "bag"}}, "categories": {"fruits": "Fruits", "vegetables": "Vegetables", "meat": "Meat", "seafood": "Seafood", "dairy": "Dairy", "grains": "Grains", "legumes": "Legumes", "nuts": "Nuts", "spices": "Spices", "herbs": "<PERSON><PERSON>", "oils": "Oils", "condiments": "Condiments", "beverages": "Beverages", "snacks": "Snacks", "frozen": "Frozen", "canned": "Canned", "baking": "Baking", "other": "Other"}}